name: Build binaries for UnblockNeteaseMusic

on:
    push:
        branches:
            - 'enhanced'
        paths-ignore:
            - '**.md'
            - 'precompiled/*.js'

    pull_request:
        types:
            - opened
            - synchronize
            - reopened
        paths-ignore:
            - '**.md'
            - 'precompiled/*.js'

    release:
        types:
            - published

jobs:
    build:
        strategy:
            matrix:
                target:
                    - 'linux-arm64'
                    - 'linux-x64'
                    - 'win-arm64'
                    - 'win-x64'
            fail-fast: false

        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: yarn

            - name: Set up QEMU
              id: qemu
              uses: docker/setup-qemu-action@v3
              with:
                  image: tonistiigi/binfmt:latest
                  platforms: all

            - name: Install dependencies
              run: yarn

            - name: Make bundle
              run: yarn build

            - name: Building executable file for ${{ matrix.target }}
              run: |
                  npx pkg --compress Brotli -t node18-${{ matrix.target }} .
                  mv "$(ls dist/*)" "$(ls dist/* | sed 's/server/unblockneteasemusic-${{ matrix.target }}/g')"
                  echo "ASSET_NAME=$(ls dist)" >> $GITHUB_ENV

            - name: Publishing to GitHub Artifacts
              uses: actions/upload-artifact@v4
              if: github.event_name != 'release'
              with:
                  name: ${{ env.ASSET_NAME }}
                  path: |
                      ./dist/*

            - name: Publishing to GitHub Releases
              uses: svenstaro/upload-release-action@2.11.2
              if: github.event_name == 'release'
              with:
                  repo_token: ${{ github.token }}
                  file: dist/*
                  tag: ${{ github.ref }}
                  file_glob: true
