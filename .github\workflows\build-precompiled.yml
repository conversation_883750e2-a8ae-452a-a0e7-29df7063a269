name: <PERSON>uild precompiled JavaScript file

on:
    push:
        branches:
            - enhanced
        paths:
            - '*.js'
            - 'src/**.js'
            - '.github/workflows/*.yml'
            - 'package.json'
            - 'yarn.lock'
    workflow_dispatch:

jobs:
    build-precompiled:
        runs-on: ubuntu-latest
        permissions:
            contents: write
            pull-requests: write

        steps:
            - name: Checkout
              uses: actions/checkout@v4

            - name: Cleanup precompiled
              run: rm -rf precompiled/*

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: yarn

            - name: Install dependencies
              run: yarn

            - name: Build the precompiled package
              run: yarn build

            - name: Create Pull Request
              uses: peter-evans/create-pull-request@v7
              with:
                  token: ${{ github.token }}
                  commit-message: 'build(precompiled): make a bundle with the dependencies'
                  author: 'github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>'
                  branch: ci/build-precompiled
                  delete-branch: true
                  title: 'build(precompiled): make a bundle with the dependencies'
                  body: |
                      Merge till a new release tagged.
                  labels: ci:build
