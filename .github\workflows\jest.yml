name: Test UNM with Jest

on:
    push:
        branches:
            - 'enhanced'
        paths:
            - '*.js'
            - 'package.json'
            - 'src/**.js'
            - 'yarn.lock'
            - '.github/workflows/jest.yml'

    pull_request:
        types:
            - opened
            - synchronize
            - reopened
        paths:
            - '*.js'
            - 'package.json'
            - 'src/**.js'
            - 'yarn.lock'
            - '.github/workflows/jest.yml'

    # Allows you to run this workflow manually from the Actions tab
    workflow_dispatch:

jobs:
    test-sources:
        runs-on: ubuntu-latest
        steps:
            # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
            - name: Checkout
              uses: actions/checkout@v4

            - name: Use Node.js 20
              uses: actions/setup-node@v4
              with:
                  node-version: 20
                  cache: yarn

            - name: Install dependencies
              run: yarn

            - name: Test the sources with Jest
              run: yarn test
